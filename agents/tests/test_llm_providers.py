"""
Tests for the LLM provider implementations
"""

import unittest
import asyncio
import os
from unittest.mock import MagicMock, patch

from agents import LLMProvider
from agents.llm_providers import GeminiProvider, OpenAIProvider

class TestGeminiProvider(unittest.TestCase):
    """Test cases for the GeminiProvider class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Skip tests if no API key is available
        self.api_key = os.environ.get("GEMINI_API_KEY")
        if not self.api_key:
            self.skipTest("No Gemini API key available")
        
        self.provider = GeminiProvider(api_key=self.api_key)
    
    def test_initialization(self):
        """Test provider initialization."""
        provider = GeminiProvider(api_key="test_key")
        self.assertEqual(provider.api_key, "test_key")
        self.assertEqual(provider.model, "gemini-pro")
        self.assertEqual(provider.temperature, 0.7)
    
    @patch('google.generativeai.GenerativeModel')
    def test_generate(self, mock_generative_model):
        """Test the generate method."""
        # Mock the GenerativeModel class
        mock_model_instance = MagicMock()
        mock_generative_model.return_value = mock_model_instance
        
        # Mock the generate_content method
        mock_response = MagicMock()
        mock_response.text = "Mock response"
        mock_model_instance.generate_content.return_value = mock_response
        
        # Create a provider with a mock API key
        provider = GeminiProvider(api_key="test_key")
        
        # Generate text
        response = asyncio.run(provider.generate("Test prompt"))
        
        # Check the response
        self.assertEqual(response, "Mock response")
        
        # Check that the model was created with the correct parameters
        mock_generative_model.assert_called_once()
        self.assertEqual(mock_generative_model.call_args[1]["model_name"], "gemini-pro")
        
        # Check that generate_content was called with the correct prompt
        mock_model_instance.generate_content.assert_called_once_with("Test prompt")

class TestOpenAIProvider(unittest.TestCase):
    """Test cases for the OpenAIProvider class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Skip tests if no API key is available
        self.api_key = os.environ.get("OPENAI_API_KEY")
        if not self.api_key:
            self.skipTest("No OpenAI API key available")
        
        self.provider = OpenAIProvider(api_key=self.api_key)
    
    def test_initialization(self):
        """Test provider initialization."""
        provider = OpenAIProvider(api_key="test_key")
        self.assertEqual(provider.api_key, "test_key")
        self.assertEqual(provider.model, "gpt-3.5-turbo")
        self.assertEqual(provider.temperature, 0.7)
    
    @patch('openai.AsyncOpenAI')
    def test_generate(self, mock_async_openai):
        """Test the generate method."""
        # Mock the AsyncOpenAI class
        mock_client = MagicMock()
        mock_async_openai.return_value = mock_client
        
        # Mock the chat.completions.create method
        mock_chat = MagicMock()
        mock_client.chat = mock_chat
        mock_completions = MagicMock()
        mock_chat.completions = mock_completions
        
        # Mock the create method
        mock_response = MagicMock()
        mock_choice = MagicMock()
        mock_message = MagicMock()
        mock_message.content = "Mock response"
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        
        # Create a future to return from the create method
        future = asyncio.Future()
        future.set_result(mock_response)
        mock_completions.create = MagicMock(return_value=future)
        
        # Create a provider with a mock API key
        provider = OpenAIProvider(api_key="test_key")
        
        # Generate text
        response = asyncio.run(provider.generate("Test prompt"))
        
        # Check the response
        self.assertEqual(response, "Mock response")
        
        # Check that the client was created with the correct API key
        mock_async_openai.assert_called_once_with(api_key="test_key")
        
        # Check that create was called with the correct parameters
        mock_completions.create.assert_called_once()
        call_args = mock_completions.create.call_args[1]
        self.assertEqual(call_args["model"], "gpt-3.5-turbo")
        self.assertEqual(call_args["messages"][1]["content"], "Test prompt")

if __name__ == "__main__":
    unittest.main()
