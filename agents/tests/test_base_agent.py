"""
Tests for the BaseAgent class
"""

import unittest
import asyncio
from unittest.mock import MagicMock, patch

from agents import (
    BaseAgent,
    AgentContext,
    AgentAction,
    ActionResult,
    ContextType,
    AgentTask
)

class TestBaseAgent(unittest.TestCase):
    """Test cases for the BaseAgent class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a concrete implementation of BaseAgent for testing
        class ConcreteAgent(BaseAgent):
            async def get_next_action(self, context):
                return AgentAction(action_type="TEST")
                
            async def execute_action(self, action):
                return ActionResult(success=True, result="Test result")
        
        self.agent_class = ConcreteAgent
        self.agent = self.agent_class("test_agent", "Test Agent")
    
    def test_initialization(self):
        """Test agent initialization."""
        # Test with two parameters
        agent = self.agent_class("test_id", "Test Name")
        self.assertEqual(agent.id, "test_id")
        self.assertEqual(agent.name, "Test Name")
        
        # Test with one parameter
        agent = self.agent_class("Test Name")
        self.assertEqual(agent.name, "Test Name")
        self.assertIsNotNone(agent.id)  # Should generate a UUID
    
    def test_can_handle(self):
        """Test the can_handle method."""
        # Create a task that the agent can handle
        task = AgentTask(
            id="task1",
            description="test agent task",
            priority=5
        )
        
        # Agent should be able to handle this task
        self.assertTrue(self.agent.can_handle(task))
        
        # Create a task that the agent cannot handle
        task = AgentTask(
            id="task2",
            description="unrelated task",
            priority=5
        )
        
        # Agent should not be able to handle this task
        self.assertFalse(self.agent.can_handle(task))
    
    def test_process_task(self):
        """Test the process_task method."""
        # Create a task
        task = AgentTask(
            id="task1",
            description="test task",
            priority=5
        )
        
        # Run the process_task method
        result = asyncio.run(self.agent.process_task(task))
        
        # Check the result
        self.assertEqual(result["task_id"], "task1")
        self.assertEqual(result["status"], "completed")
        self.assertEqual(result["result"], "Test result")

if __name__ == "__main__":
    unittest.main()
