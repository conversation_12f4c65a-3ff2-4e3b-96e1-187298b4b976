"""API endpoints for video generation using Stability AI v2beta."""

import logging
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form

from app.schemas.video import (
    ImageToVideoRequest,
    FrontendVideoResponse,
    VideoGenerationConstants
)
from app.services.video_service import video_service

logger = logging.getLogger(__name__)
router = APIRouter()

async def verify_api_key():
    """Verify API key for video generation endpoints."""
    # In development mode, skip API key verification
    logger.debug("API Key verification skipped in development mode")
    return

@router.post(
    "/image-to-video",
    response_model=FrontendVideoResponse,
    dependencies=[Depends(verify_api_key)],
    summary="Start image-to-video generation",
    description="Start generating a video from an uploaded image using Stability AI's Stable Video Diffusion model."
)
async def start_image_to_video_generation(
    image: UploadFile = File(..., description="The source image file (JPEG/PNG, max 10MB)"),
    seed: Optional[int] = Form(0, description="Random seed for generation (0 for random)"),
    cfg_scale: Optional[float] = Form(1.8, description="How strongly the video sticks to the original image (0-10)"),
    motion_bucket_id: Optional[int] = Form(127, description="Amount of motion in the video (1-255)")
) -> FrontendVideoResponse:
    """
    Start image-to-video generation.

    This endpoint accepts an image file and parameters to start video generation.
    Returns a generation ID that can be used to poll for results.

    **Supported Image Formats:** JPEG, PNG
    **Supported Dimensions:** 1024x576, 576x1024, 768x768 (images will be automatically resized)
    **Maximum File Size:** 10MB

    **Parameters:**
    - **image**: The source image file
    - **seed**: Random seed (0-4294967294, 0 = random)
    - **cfg_scale**: Adherence to original image (0-10, default: 1.8)
    - **motion_bucket_id**: Amount of motion (1-255, default: 127)
    """
    logger.info(f"Received image-to-video request: seed={seed}, cfg_scale={cfg_scale}, motion_bucket_id={motion_bucket_id}")

    try:
        # Validate parameters
        if seed < VideoGenerationConstants.MIN_SEED or seed > VideoGenerationConstants.MAX_SEED:
            raise HTTPException(
                status_code=400,
                detail=f"Seed must be between {VideoGenerationConstants.MIN_SEED} and {VideoGenerationConstants.MAX_SEED}"
            )

        if cfg_scale < VideoGenerationConstants.MIN_CFG_SCALE or cfg_scale > VideoGenerationConstants.MAX_CFG_SCALE:
            raise HTTPException(
                status_code=400,
                detail=f"cfg_scale must be between {VideoGenerationConstants.MIN_CFG_SCALE} and {VideoGenerationConstants.MAX_CFG_SCALE}"
            )

        if motion_bucket_id < VideoGenerationConstants.MIN_MOTION_BUCKET_ID or motion_bucket_id > VideoGenerationConstants.MAX_MOTION_BUCKET_ID:
            raise HTTPException(
                status_code=400,
                detail=f"motion_bucket_id must be between {VideoGenerationConstants.MIN_MOTION_BUCKET_ID} and {VideoGenerationConstants.MAX_MOTION_BUCKET_ID}"
            )

        # Validate image file
        if not image.filename:
            raise HTTPException(status_code=400, detail="No image file provided")

        # Create request object
        request = ImageToVideoRequest(
            seed=seed,
            cfg_scale=cfg_scale,
            motion_bucket_id=motion_bucket_id
        )

        # Start generation
        result = await video_service.start_image_to_video_generation(image, request)

        logger.info(f"Video generation started successfully with ID: {result.id}")

        return FrontendVideoResponse(
            success=True,
            id=result.id,
            status="processing",
            metadata={
                "seed": seed,
                "cfg_scale": cfg_scale,
                "motion_bucket_id": motion_bucket_id,
                "image_filename": image.filename
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in image-to-video endpoint: {str(e)}", exc_info=True)
        return FrontendVideoResponse(
            success=False,
            error=f"Failed to start video generation: {str(e)}"
        )

@router.get(
    "/image-to-video/{generation_id}",
    response_model=FrontendVideoResponse,
    dependencies=[Depends(verify_api_key)],
    summary="Get image-to-video generation result",
    description="Check the status and get the result of an image-to-video generation by ID."
)
async def get_image_to_video_result(generation_id: str) -> FrontendVideoResponse:
    """
    Get the result of an image-to-video generation.

    **Status Values:**
    - **processing**: Generation is still in progress
    - **completed**: Generation completed successfully
    - **failed**: Generation failed

    **Polling Recommendations:**
    - Poll every 10 seconds
    - Maximum wait time: 10 minutes
    - Video generation typically takes 2-5 minutes
    """
    logger.info(f"Checking video generation result for ID: {generation_id}")

    try:
        if not generation_id or generation_id.strip() == "":
            raise HTTPException(status_code=400, detail="Generation ID is required")

        # Get result from service
        result = await video_service.get_video_generation_result(generation_id)

        if result.status == "in-progress":
            return FrontendVideoResponse(
                success=True,
                id=result.id,
                status="processing",
                metadata={"message": "Video generation is still in progress"}
            )
        elif result.status == "completed":
            # Convert base64 video to data URL for frontend
            video_data_url = f"data:video/mp4;base64,{result.video}" if result.video else None

            logger.info(f"Video generation {generation_id} completed successfully")

            return FrontendVideoResponse(
                success=True,
                id=result.id,
                status="completed",
                video_url=video_data_url,
                metadata={
                    "finish_reason": result.finish_reason,
                    "seed": result.seed,
                    "message": "Video generation completed successfully"
                }
            )
        else:  # failed
            logger.error(f"Video generation {generation_id} failed: {result.error}")

            return FrontendVideoResponse(
                success=False,
                id=result.id,
                status="failed",
                error=result.error or "Video generation failed"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting video result: {str(e)}", exc_info=True)
        return FrontendVideoResponse(
            success=False,
            error=f"Failed to get video generation result: {str(e)}"
        )

@router.get(
    "/image-to-video/{generation_id}/status",
    response_model=dict,
    dependencies=[Depends(verify_api_key)],
    summary="Get generation status only",
    description="Get only the status of a video generation (lightweight endpoint for polling)."
)
async def get_generation_status(generation_id: str):
    """
    Get only the status of a video generation.

    This is a lightweight endpoint for frequent polling that returns minimal data.
    """
    logger.info(f"Checking generation status for ID: {generation_id}")

    try:
        if not generation_id or generation_id.strip() == "":
            raise HTTPException(status_code=400, detail="Generation ID is required")

        result = await video_service.get_video_generation_result(generation_id)

        return {
            "id": result.id,
            "status": "processing" if result.status == "in-progress" else result.status,
            "completed": result.status == "completed",
            "failed": result.status == "failed"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking generation status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to check generation status: {str(e)}"
        )

@router.get(
    "/supported-formats",
    summary="Get supported image formats and dimensions",
    description="Get information about supported image formats and dimensions for video generation."
)
async def get_supported_formats():
    """Get supported image formats and dimensions."""
    return {
        "supported_formats": VideoGenerationConstants.SUPPORTED_IMAGE_FORMATS,
        "supported_dimensions": VideoGenerationConstants.SUPPORTED_DIMENSIONS,
        "max_file_size_mb": VideoGenerationConstants.MAX_FILE_SIZE_BYTES / (1024 * 1024),
        "parameter_limits": {
            "seed": {
                "min": VideoGenerationConstants.MIN_SEED,
                "max": VideoGenerationConstants.MAX_SEED,
                "default": 0
            },
            "cfg_scale": {
                "min": VideoGenerationConstants.MIN_CFG_SCALE,
                "max": VideoGenerationConstants.MAX_CFG_SCALE,
                "default": VideoGenerationConstants.DEFAULT_CFG_SCALE
            },
            "motion_bucket_id": {
                "min": VideoGenerationConstants.MIN_MOTION_BUCKET_ID,
                "max": VideoGenerationConstants.MAX_MOTION_BUCKET_ID,
                "default": VideoGenerationConstants.DEFAULT_MOTION_BUCKET_ID
            }
        },
        "polling": {
            "interval_seconds": VideoGenerationConstants.POLL_INTERVAL_SECONDS,
            "max_attempts": VideoGenerationConstants.MAX_POLL_ATTEMPTS
        }
    }


