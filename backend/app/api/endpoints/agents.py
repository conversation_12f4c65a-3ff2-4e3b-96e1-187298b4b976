"""
Agent endpoints for Emma Studio API.

This module provides endpoints for interacting with the agent system,
including getting available agents and chatting with specific agents.
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any
import logging

from app.schemas.crew import AgentChatRequest, AgentChatResponse
from app.services.agent_service import get_available_agents, chat_with_agent

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/agents", response_model=List[Dict[str, Any]])
async def list_available_agents():
    """
    Get a list of all available agents.

    Returns:
        List of available agents with their metadata
    """
    try:
        agents = get_available_agents()
        logger.info(f"Retrieved {len(agents)} available agents")
        return agents
    except Exception as e:
        logger.error(f"Error retrieving agents: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving agents: {str(e)}")

@router.post("/agent/chat", response_model=AgentChatResponse)
async def chat_with_specific_agent(request: AgentChatRequest):
    """
    Chat with a specific agent using smart routing.

    This endpoint uses intelligent routing to determine whether to:
    - Route directly to the requested agent (bypassing <PERSON>)
    - Route through Emma for orchestration
    - Coordinate multiple agents for complex requests

    Args:
        request: Agent chat request containing agent_id, message, and context

    Returns:
        Agent response with the generated content and routing information
    """
    try:
        logger.info(f"Processing smart-routed chat request for agent {request.agent_id}")
        response = await chat_with_agent(request)
        logger.info(f"Smart-routed chat completed successfully for agent {request.agent_id}")
        return response
    except Exception as e:
        logger.error(f"Error in smart-routed agent chat: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error in agent chat: {str(e)}")


@router.post("/agent/routing-analysis")
async def analyze_routing(request: AgentChatRequest):
    """
    Analyze how a request would be routed without executing it.

    This endpoint shows the smart routing decision for a given request,
    helping developers understand the routing logic.

    Args:
        request: Agent chat request to analyze

    Returns:
        Routing analysis with decision, reasoning, and confidence
    """
    try:
        from app.services.agent_service import agent_service

        # Get routing analysis
        routing_result = agent_service.config.smart_routing.analyze_request(request)

        return {
            "routing_decision": routing_result.decision.value,
            "target_agent_id": routing_result.target_agent_id,
            "target_agents": routing_result.target_agents,
            "confidence": routing_result.confidence,
            "reasoning": routing_result.reasoning,
            "metadata": routing_result.metadata,
            "explanation": agent_service.config.smart_routing.get_routing_explanation(request)
        }
    except Exception as e:
        logger.error(f"Error in routing analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error in routing analysis: {str(e)}")


@router.get("/agent/{agent_id}")
async def get_agent_info(agent_id: str):
    """
    Get information about a specific agent.

    Args:
        agent_id: The ID of the agent to get information for

    Returns:
        Agent information and metadata
    """
    try:
        agents = get_available_agents()
        agent = next((a for a in agents if a["id"] == agent_id), None)

        if not agent:
            raise HTTPException(status_code=404, detail=f"Agent {agent_id} not found")

        logger.info(f"Retrieved information for agent {agent_id}")
        return agent
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving agent {agent_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving agent: {str(e)}")

@router.get("/agent/{agent_id}/capabilities")
async def get_agent_capabilities(agent_id: str):
    """
    Get the capabilities of a specific agent.

    Args:
        agent_id: The ID of the agent to get capabilities for

    Returns:
        Agent capabilities and specialties
    """
    try:
        agents = get_available_agents()
        agent = next((a for a in agents if a["id"] == agent_id), None)

        if not agent:
            raise HTTPException(status_code=404, detail=f"Agent {agent_id} not found")

        capabilities = {
            "agent_id": agent_id,
            "name": agent["name"],
            "role": agent["role"],
            "specialties": agent.get("specialties", []),
            "description": agent.get("description", ""),
            "capabilities": agent.get("capabilities", [])
        }

        logger.info(f"Retrieved capabilities for agent {agent_id}")
        return capabilities
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving capabilities for agent {agent_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving capabilities: {str(e)}")
