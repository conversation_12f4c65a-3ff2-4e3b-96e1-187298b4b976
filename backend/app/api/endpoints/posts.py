"""
API endpoints for post generation using multiple AI providers.
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

from app.services.gpt_image_service import generate_gpt_image
from app.services.image_service import generate_image
from app.services.meme_service import MemeService
from app.schemas.image import GPTImageGenerationRequest, ImageGenerationRequest

logger = logging.getLogger(__name__)
router = APIRouter()

# Initialize meme service
meme_service = MemeService()


class PostGenerationRequest(BaseModel):
    """Request schema for post generation."""
    # Brand information from steps 1-3
    brandInfo: Dict[str, Any] = Field(..., description="Complete brand information")
    
    # Design configuration from step 4
    designConfig: Dict[str, Any] = Field(..., description="Design and template configuration")
    
    # Generation configuration
    generationConfig: Dict[str, Any] = Field(default_factory=dict, description="Generation settings")


class GeneratedPost(BaseModel):
    """Schema for a single generated post."""
    id: str = Field(..., description="Unique post ID")
    text: str = Field(..., description="Generated text content")
    image_url: Optional[str] = Field(None, description="Generated image URL")
    template: str = Field(..., description="Template used")
    platform: str = Field(..., description="Target platform")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class PostGenerationResponse(BaseModel):
    """Response schema for post generation."""
    success: bool = Field(..., description="Whether generation was successful")
    posts: List[GeneratedPost] = Field(default_factory=list, description="Generated posts")
    total_generated: int = Field(0, description="Total number of posts generated")
    error: Optional[str] = Field(None, description="Error message if generation failed")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Generation metadata")


def get_platform_dimensions(platform: str, content_type: str) -> str:
    """
    Get platform-specific image dimensions.

    Args:
        platform: Target platform (Instagram, Facebook, LinkedIn, etc.)
        content_type: Type of content (posts, stories, etc.)

    Returns:
        Dimension string in format "WIDTHxHEIGHT"
    """
    platform_dimensions = {
        "Instagram": {
            "instagram_posts": "1080x1080",  # Square posts
            "instagram_stories": "1080x1920",  # Vertical stories
            "default": "1080x1080"
        },
        "Facebook": {
            "facebook_posts": "1200x630",  # Landscape posts
            "default": "1200x630"
        },
        "LinkedIn": {
            "linkedin_posts": "1200x627",  # Professional posts
            "default": "1200x627"
        },
        "X (Twitter)": {
            "twitter_posts": "1200x675",  # Twitter posts
            "default": "1200x675"
        }
    }

    platform_config = platform_dimensions.get(platform, platform_dimensions["Instagram"])
    return platform_config.get(content_type, platform_config["default"])


def select_image_provider(template: str, content_description: str) -> str:
    """
    Intelligently select image provider based on template and content.

    PRIORITY ORDER (REVERSED):
    1. LOCAL MEME: Template-based memes (cost-effective)
    2. STABILITY AI: Visual content (landscapes, products, scenes) - PRIMARY
    3. OPENAI GPT-IMAGE-1: Text-heavy images (quotes, comics) - SECONDARY

    Args:
        template: The selected template name
        content_description: Description of the content to generate

    Returns:
        Provider name: 'meme', 'stability', or 'gpt-image-1'
    """
    content_lower = content_description.lower()

    # LOCAL MEME service for template-based memes (TERTIARY - cost optimization)
    if template == "Meme" or any(keyword in content_lower for keyword in [
        "meme", "humor", "gracioso", "divertido", "vs", "antes", "después", "template"
    ]):
        return "meme"

    # OPENAI GPT-IMAGE-1 for text-heavy content (SECONDARY)
    text_heavy_keywords = [
        "quote", "cita", "texto", "frase", "motivacional", "inspiracional",
        "comic", "caricatura", "meme con texto", "overlay", "tipografía"
    ]

    if any(keyword in content_lower for keyword in text_heavy_keywords):
        return "gpt-image-1"

    # STABILITY AI for visual content (PRIMARY - default for most content)
    # This includes: landscapes, products, scenes, graphics, professional photography
    return "stability"


def generate_template_prompt(template: str, brand_info: Dict[str, Any], platform: str) -> str:
    """
    Generate specific prompt based on template and brand information.
    
    Args:
        template: Template name
        brand_info: Brand information
        platform: Target platform
        
    Returns:
        Generated prompt string
    """
    business_name = brand_info.get("businessName", "Business")
    
    base_prompts = {
        # EDUCATIVO/PROFESIONAL
        "Balance": f"Crea posts educativos y profesionales sobre {business_name}. Enfócate en tips empresariales, estadísticas relevantes de la industria, y consejos prácticos. Mantén un tono equilibrado entre profesional y accesible.",
        
        "Interface": f"Genera posts tipo tutorial y tech para {business_name}. Incluye procesos paso a paso, explicaciones técnicas simplificadas, y contenido que eduque sobre tecnología o metodologías.",
        
        "Fonts": f"Crea posts con quotes profesionales y frases inspiradoras de líderes para {business_name}. Enfócate en citas motivacionales del mundo empresarial y frases que reflejen los valores de la marca.",
        
        "Educational": f"Desarrolla contenido puramente educativo para {business_name}. Crea posts que enseñen algo nuevo, expliquen conceptos complejos de forma simple, y aporten valor educativo real.",
        
        "Informativo": f"Genera posts informativos con noticias relevantes de la industria de {business_name}. Incluye tendencias del sector, actualizaciones importantes, y análisis de mercado.",

        # MOTIVACIONAL/INSPIRACIONAL
        "Motivational": f"Crea posts motivacionales e inspiradores para {business_name}. Enfócate en frases que inspiren acción, historias de superación, y contenido que motive a la audiencia.",
        
        "Success": f"Desarrolla posts sobre historias de éxito relacionadas con {business_name}. Incluye casos de éxito, logros alcanzados, y testimonios inspiradores.",
        
        "Mindset": f"Genera contenido sobre mentalidad y crecimiento personal para {business_name}. Enfócate en desarrollo personal, cambio de perspectiva, y mindset empresarial.",

        # ENTRETENIMIENTO/VIRAL
        "Meme": f"Crea posts con humor y contenido relatable para {business_name}. Usa memes relevantes a la industria, situaciones divertidas del día a día empresarial, y humor inteligente.",
        
        "Comic": f"Desarrolla posts con ilustraciones divertidas y storytelling visual para {business_name}. Crea narrativas visuales, historias cortas con moraleja, y contenido que entretenga.",
        
        "Fun Facts": f"Genera posts con datos curiosos y '¿sabías que?' relacionados con {business_name} y su industria. Incluye estadísticas sorprendentes y hechos interesantes.",

        # HUMANO/PERSONAL
        "Influencer": f"Crea posts estilo personal branding y behind the scenes para {business_name}. Muestra el lado humano de la marca, historias personales, y contenido auténtico.",
        
        "Story": f"Desarrolla posts narrativos con experiencias reales de {business_name}. Cuenta historias de la empresa, anécdotas significativas, y experiencias que conecten emocionalmente.",
        
        "Behind Scenes": f"Genera contenido detrás de cámaras del equipo de {business_name}. Muestra el proceso de trabajo, el día a día del equipo, y la cultura empresarial.",

        # REDES ESPECÍFICAS
        "Tweet": f"Crea posts estilo Twitter con texto-heavy, hilos y micro-content para {business_name}. Enfócate en mensajes concisos, hilos informativos, y contenido fácil de compartir.",
        
        "Instagram Story": f"Desarrolla contenido vertical e interactivo para {business_name}. Crea posts optimizados para stories, con elementos visuales llamativos y CTAs claros.",
        
        "LinkedIn Post": f"Genera posts profesionales optimizados para networking de {business_name}. Enfócate en contenido B2B, insights de industria, y networking profesional.",

        # PRODUCTO/VENTAS
        "Simply Image": f"Crea posts de product showcase con diseño limpio para {business_name}. Enfócate en mostrar productos/servicios de forma elegante y minimalista.",
        
        "Before/After": f"Desarrolla posts mostrando transformaciones y resultados de {business_name}. Incluye casos antes/después, mejoras logradas, y resultados tangibles.",
        
        "Announcement": f"Genera posts de lanzamientos y novedades para {business_name}. Anuncia nuevos productos, servicios, o actualizaciones importantes de la empresa."
    }

    platform_specific = {
        "Instagram": "Optimiza para Instagram con hashtags relevantes, formato cuadrado, y estilo visual atractivo.",
        "Facebook": "Adapta para Facebook con texto más largo, enfoque en engagement, y formato que invite a comentarios.",
        "LinkedIn": "Profesionaliza para LinkedIn con tono B2B, networking focus, y contenido de valor empresarial.",
        "X (Twitter)": "Simplifica para X con mensajes concisos, hashtags trending, y formato fácil de retwittear."
    }

    base_prompt = base_prompts.get(template, base_prompts["Balance"])
    platform_prompt = platform_specific.get(platform, platform_specific["Instagram"])
    
    return f"{base_prompt} {platform_prompt}"


def generate_image_prompt(template: str, brand_info: Dict[str, Any], post_text: str, platform: str) -> str:
    """
    Generate SEPARATE visual description that complements (not repeats) the text.
    Creates engaging visuals that enhance the message without duplicating it.

    Args:
        template: Template name
        brand_info: Brand information
        post_text: The actual post text content
        platform: Target platform

    Returns:
        Visual description for image generation
    """
    business_name = brand_info.get("businessName", "Business")
    industry = brand_info.get("industry", "general")

    # Visual prompts that COMPLEMENT text content, don't repeat it
    visual_prompts = {
        "Balance": [
            f"Modern minimalist workspace with {business_name} branding, clean desk setup, soft natural lighting",
            f"Professional {industry} environment, sleek modern design, brand colors, high-end aesthetic",
            f"Elegant product photography style, premium feel, sophisticated composition"
        ],
        "Informational": [
            f"Clean infographic-style background, data visualization elements, professional charts and graphs",
            f"Modern office setting with digital screens, technology focus, {industry} context",
            f"Abstract geometric patterns representing growth and innovation, corporate aesthetic"
        ],
        "Motivational": [
            f"Inspiring sunrise landscape, mountain peaks, achievement symbolism, uplifting atmosphere",
            f"Success imagery: climbing stairs, reaching goals, professional achievement context",
            f"Energetic workspace, team collaboration, positive business environment"
        ],
        "Educational": [
            f"Modern classroom or workshop setting, learning environment, knowledge sharing atmosphere",
            f"Books, digital devices, educational tools arranged aesthetically, {industry} focus",
            f"Professional training environment, clean presentation setup, educational context"
        ],
        "Comic": [
            f"Playful cartoon-style illustration, bright colors, fun business scenario, lighthearted tone",
            f"Humorous office situation, cartoon characters, professional but fun atmosphere",
            f"Comic book style panels, vibrant colors, entertaining business context"
        ],
        "Entertainment": [
            f"Dynamic action scene, exciting business moment, energetic composition, vibrant colors",
            f"Fun social media style photo, engaging visual elements, entertainment industry vibe",
            f"Creative artistic composition, bold colors, eye-catching design elements"
        ]
    }

    # Select appropriate visual prompt
    prompts = visual_prompts.get(template, visual_prompts["Balance"])
    selected_prompt = prompts[hash(post_text) % len(prompts)]

    # Add platform-specific optimization
    platform_additions = {
        "Instagram": "square format, Instagram-optimized, social media aesthetic",
        "LinkedIn": "professional business setting, corporate aesthetic, LinkedIn-appropriate",
        "Facebook": "engaging social media style, Facebook-optimized, community feel",
        "X": "dynamic composition, Twitter-style visual, concise visual impact"
    }

    platform_addition = platform_additions.get(platform, platform_additions["Instagram"])

    return f"{selected_prompt}, {platform_addition}, high quality, professional photography"


def generate_post_text(template: str, brand_info: Dict[str, Any], index: int) -> str:
    """
    Generate engaging social media copy that users will read.
    This is SEPARATE from image generation - focuses on text content only.

    Args:
        template: Template name
        brand_info: Brand information
        index: Post index for variation

    Returns:
        Generated post text for social media
    """
    business_name = brand_info.get("businessName", "Tu Negocio")

    mock_texts = {
        "Balance": [
            f"🎯 En {business_name}, creemos que el equilibrio entre innovación y tradición es clave para el éxito. ¿Cuál es tu estrategia? #Negocios #Estrategia",
            f"💡 Tip del día: La consistencia en {business_name} nos ha llevado a donde estamos hoy. Pequeños pasos, grandes resultados. #Motivación #Emprendimiento",
            f"📊 Datos que importan: El 80% de nuestros clientes en {business_name} valoran la calidad sobre el precio. ¿Qué valoras tú? #Calidad #Clientes"
        ],
        "Motivational": [
            f"🚀 'El éxito no es el destino, es el viaje' - Equipo {business_name}. Cada día es una nueva oportunidad para crecer. #Motivación #Éxito",
            f"💪 En {business_name} sabemos que los obstáculos son oportunidades disfrazadas. ¿Cuál fue tu mayor aprendizaje este año? #Crecimiento #Mindset",
            f"⭐ Recordatorio: Tu potencial es ilimitado. En {business_name} creemos en ti tanto como tú debes creer en ti mismo. #Inspiración #Potencial"
        ],
        "Meme": [
            f"Cuando alguien pregunta si {business_name} es la mejor opción... 😎 *Drake pointing* ¡Por supuesto que sí! #Humor #Confianza",
            f"Yo: 'Solo voy a revisar {business_name} 5 minutos' También yo: *3 horas después* 🤯 #Relatable #Adicción",
            f"Expectativa vs Realidad con {business_name}: Expectativa: Usar 1 hora. Realidad: Toda la tarde porque es increíble 😅 #Humor #Realidad"
        ],
        "Educational": [
            f"📚 ¿Sabías que? En {business_name} aplicamos metodologías ágiles para optimizar resultados. Aquí te explicamos cómo: #Educación #Metodología",
            f"🎓 Tutorial rápido: 3 pasos para maximizar tu experiencia con {business_name}. ¡Guarda este post! #Tutorial #Tips",
            f"🔍 Análisis profundo: Por qué {business_name} utiliza esta tecnología y cómo te beneficia. Hilo 🧵 #Análisis #Tecnología"
        ],
        "Informativo": [
            f"📰 Últimas noticias: {business_name} se adapta a las nuevas tendencias del mercado. Te contamos todo aquí 👇 #Noticias #Tendencias",
            f"📈 Reporte de industria: Las métricas que importan en 2024 según {business_name}. Datos actualizados. #Reporte #Industria",
            f"🔔 Actualización importante: {business_name} implementa nuevas funcionalidades basadas en feedback de usuarios. #Actualización #Mejoras"
        ]
    }

    # Get texts for the template, fallback to Balance if not found
    template_texts = mock_texts.get(template, mock_texts["Balance"])

    # Return text based on index, cycle through available texts
    return template_texts[(index - 1) % len(template_texts)]


@router.post("/generate", response_model=PostGenerationResponse)
async def generate_posts(request: PostGenerationRequest) -> PostGenerationResponse:
    """
    Generate social media posts using AI providers based on brand data and template selection.
    
    This endpoint:
    1. Uses Gemini for text/copy generation
    2. Uses GPT-4 gpt-image-1 for conceptual images (memes, quotes, etc.)
    3. Uses Stability AI for epic/creative photorealistic images
    4. Generates 10 unique posts based on the selected template
    """
    logger.info("🚀 Starting post generation process")
    
    try:
        brand_info = request.brandInfo
        design_config = request.designConfig
        generation_config = request.generationConfig
        
        template = design_config.get("selectedTheme", "Balance")
        platform = design_config.get("platform", "Instagram")
        content_type = design_config.get("contentType", "instagram_posts")
        
        logger.info(f"📋 Generating posts: template={template}, platform={platform}")
        
        # Generate template-specific prompt
        template_prompt = generate_template_prompt(template, brand_info, platform)
        
        # Generate the specified number of posts (default 3)
        post_count = generation_config.get("count", 3)

        # Get platform-specific dimensions
        image_dimensions = get_platform_dimensions(platform, content_type)
        logger.info(f"🖼️ Using platform-specific dimensions: {image_dimensions} for {platform}")

        # Generate real posts with images
        generated_posts = []
        for i in range(post_count):
            # Generate engaging social media copy (SEPARATE from image)
            post_text = generate_post_text(template, brand_info, i+1)

            # Generate SEPARATE visual description that complements the text
            image_prompt = generate_image_prompt(template, brand_info, post_text, platform)

            # Select image provider based on template and content type
            image_provider = select_image_provider(template, image_prompt)

            # Generate image based on provider
            image_url = None
            try:
                if image_provider == "meme":
                    # LOCAL MEME service for template-based memes (cost optimization)
                    meme_result = await meme_service.generate_meme(post_text)
                    if meme_result and meme_result.get("success"):
                        image_url = meme_result.get("image_url")
                        image_provider = "meme"  # Confirm provider for metadata

                elif image_provider == "gpt-image-1":
                    # OPENAI GPT-IMAGE-1 for text-heavy content (quotes, comics, memes with text overlays)
                    # Convert dimensions to supported gpt-image-1 format
                    gpt_size = "1024x1024"  # Default fallback

                    # Map platform dimensions to supported gpt-image-1 sizes
                    if image_dimensions in ["1080x1080", "1024x1024"]:
                        gpt_size = "1024x1024"  # Square (Instagram)
                    elif "1080x1920" in image_dimensions or "1024x1792" in image_dimensions:
                        gpt_size = "1024x1792"  # Portrait (Instagram Stories)
                    elif any(dim in image_dimensions for dim in ["1200x630", "1200x627", "1200x675", "1792x1024"]):
                        gpt_size = "1792x1024"  # Landscape (Facebook, LinkedIn, X/Twitter)

                    logger.info(f"🔄 Mapping {image_dimensions} → {gpt_size} for gpt-image-1")

                    # Use separate image prompt for text-heavy content with typography focus
                    gpt_request = GPTImageGenerationRequest(
                        prompt=f"Text-heavy social media design: {image_prompt}. Include readable text overlay, typography focus, quote-style design for {platform}. Professional text composition.",
                        size=gpt_size,
                        quality="high"  # Use high quality for gpt-image-1
                    )
                    gpt_response = await generate_gpt_image(gpt_request)
                    if gpt_response.success:
                        image_url = gpt_response.image_url
                    else:
                        # Fallback to Stability AI if OpenAI fails (e.g., billing limit)
                        logger.warning(f"🔄 gpt-image-1 failed, falling back to Stability AI: {gpt_response.error}")
                        width, height = map(int, image_dimensions.split('x'))
                        stability_request = ImageGenerationRequest(
                            prompt=image_prompt,  # Use separate visual prompt that complements text
                            width=width,
                            height=height,
                            style_preset="photographic"
                        )
                        stability_response = await generate_image(stability_request)
                        if stability_response.error is None and stability_response.image:
                            # Convert base64 to data URL for frontend display
                            image_url = f"data:image/webp;base64,{stability_response.image}"
                            image_provider = "stability"  # Update provider for metadata

                elif image_provider == "stability":
                    # STABILITY AI for visual content (PRIMARY - landscapes, products, scenes, graphics)
                    width, height = map(int, image_dimensions.split('x'))
                    stability_request = ImageGenerationRequest(
                        prompt=image_prompt,  # Use separate visual prompt that complements text
                        width=width,
                        height=height,
                        style_preset="photographic"
                    )
                    stability_response = await generate_image(stability_request)
                    if stability_response.error is None and stability_response.image:
                        # Convert base64 to data URL for frontend display
                        image_url = f"data:image/webp;base64,{stability_response.image}"

                logger.info(f"✅ Generated image for post {i+1} using {image_provider}: {image_url is not None}")

            except Exception as img_error:
                logger.warning(f"⚠️ Failed to generate image for post {i+1} with {image_provider}: {img_error}")
                # Continue without image if generation fails

            post = GeneratedPost(
                id=f"post_{i+1}_{template.lower()}",
                text=post_text,
                image_url=image_url,
                template=template,
                platform=platform,
                metadata={
                    "template_prompt": template_prompt,
                    "image_prompt": image_prompt,  # Include separate image prompt for debugging
                    "brand_name": brand_info.get("businessName", "Unknown"),
                    "generation_index": i+1,
                    "provider": image_provider,
                    "image_generated": image_url is not None,
                    "dimensions": image_dimensions,
                    "content_type": content_type,
                    "content_separation": "text_and_image_prompts_separated"  # Flag for new approach
                }
            )
            generated_posts.append(post)
        
        return PostGenerationResponse(
            success=True,
            posts=generated_posts,
            total_generated=len(generated_posts),
            metadata={
                "template": template,
                "platform": platform,
                "content_type": content_type,
                "template_prompt": template_prompt,
                "images_generated": sum(1 for post in generated_posts if post.image_url is not None),
                "providers_used": list(set(post.metadata.get("provider") for post in generated_posts)),
                "dimensions": image_dimensions,
                "platform_optimized": True
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Error generating posts: {e}")
        return PostGenerationResponse(
            success=False,
            error=f"Failed to generate posts: {str(e)}"
        )


class MemeGenerationRequest(BaseModel):
    """Request schema for meme generation test."""
    prompt: str = Field(..., description="Meme text/prompt")
    business_name: str = Field(default="Business", description="Name of the business")
    industry: str = Field(default="general", description="Industry type")

@router.post("/generate-meme")
async def generate_meme_endpoint(request: MemeGenerationRequest) -> Dict[str, Any]:
    """
    Test endpoint for meme generation using hybrid approach.

    Args:
        request: Meme generation request

    Returns:
        Generated meme result
    """
    logger.info(f"🎭 Testing meme generation: {request.prompt}")

    try:
        # Create mock brand info
        brand_info = {
            "businessName": request.business_name,
            "brandAnalysis": {
                "industry": request.industry
            }
        }

        # Generate meme
        meme_result = await meme_service.generate_meme(request.prompt)

        if meme_result and meme_result.get("success"):
            return {
                "success": True,
                "meme_url": meme_result.get("image_url"),
                "prompt": request.prompt,
                "business_name": request.business_name,
                "message": "Meme generated successfully!",
                "metadata": meme_result.get("metadata", {})
            }
        else:
            return {
                "success": False,
                "error": meme_result.get("error", "Failed to generate meme"),
                "prompt": request.prompt
            }

    except Exception as e:
        logger.error(f"❌ Error in meme generation test: {e}")
        return {
            "success": False,
            "error": f"Error: {str(e)}",
            "prompt": request.prompt
        }
