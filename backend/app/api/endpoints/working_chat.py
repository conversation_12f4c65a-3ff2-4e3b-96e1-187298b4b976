"""
CHAT QUE FUNCIONA - SIN BASURA
Solo 3 agentes simples que hacen su trabajo
"""
import asyncio
import json
import time
from fastapi import APIRouter
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

router = APIRouter()

class ChatRequest(BaseModel):
    message: str

# AGENTES SIMPLES - NADA DE ABSTRACCIONES
async def emma_thinks(message: str) -> str:
    """Emma decide qué hacer"""
    if any(word in message.lower() for word in ['seo', 'posicionamiento', 'keywords', 'google']):
        if any(word in message.lower() for word in ['contenido', 'blog', 'articulo', 'campaña']):
            return "BOTH"  # Necesita SEO y Content
        return "SEO"
    elif any(word in message.lower() for word in ['contenido', 'blog', 'articulo', 'campaña', 'marketing']):
        return "CONTENT"
    return "DIRECT"

async def seo_agent(message: str) -> str:
    """SEO Specialist responde"""
    try:
        from app.services.simple_content_service import get_content_service
        service = get_content_service()

        prompt = f"""Eres un especialista en SEO. Analiza esta solicitud y proporciona recomendaciones específicas de SEO:

Solicitud: {message}

Proporciona:
1. Keywords principales
2. Estrategia de contenido SEO
3. Recomendaciones técnicas
4. Métricas a seguir

Respuesta:"""

        result = await service.generate_content(prompt)
        return result.get('content', 'Error generando análisis SEO')
    except Exception as e:
        return f"Error en SEO Agent: {str(e)}"

async def content_agent(message: str) -> str:
    """Content Creator responde"""
    try:
        from app.services.simple_content_service import get_content_service
        service = get_content_service()

        prompt = f"""Eres un creador de contenido experto. Desarrolla una estrategia de contenido para:

Solicitud: {message}

Proporciona:
1. Estrategia de contenido
2. Tipos de contenido recomendados
3. Calendario editorial
4. Temas específicos

Respuesta:"""

        result = await service.generate_content(prompt)
        return result.get('content', 'Error generando estrategia de contenido')
    except Exception as e:
        return f"Error en Content Agent: {str(e)}"

async def emma_combines(message: str, seo_response: str = "", content_response: str = "") -> str:
    """Emma combina las respuestas"""
    try:
        from app.services.simple_content_service import get_content_service
        service = get_content_service()

        if seo_response and content_response:
            prompt = f"""Soy Emma, coordinadora de agentes. Combina estas respuestas de mis especialistas:

Solicitud original: {message}

Análisis SEO:
{seo_response}

Estrategia de Contenido:
{content_response}

Crea una respuesta unificada y coherente que combine ambas perspectivas:"""

        elif seo_response:
            prompt = f"""Soy Emma. Presenta este análisis SEO de forma clara:

Solicitud: {message}
Análisis: {seo_response}

Respuesta final:"""

        elif content_response:
            prompt = f"""Soy Emma. Presenta esta estrategia de contenido de forma clara:

Solicitud: {message}
Estrategia: {content_response}

Respuesta final:"""

        else:
            prompt = f"""Soy Emma, tu asistente de marketing. Responde a esta solicitud:

{message}

Respuesta:"""

        result = await service.generate_content(prompt)
        return result.get('content', 'Error generando respuesta final')
    except Exception as e:
        return f"Error en Emma: {str(e)}"

@router.post("/stream")
async def working_chat(request: ChatRequest):
    """
    CHAT QUE FUNCIONA:
    1. Emma piensa
    2. Delega si es necesario
    3. Combina respuestas
    4. Entrega resultado
    """

    async def generate():
        try:
            # PASO 1: Emma piensa
            yield f"data: {json.dumps({'type': 'thinking', 'agent': 'emma', 'message': '🧠 Emma analizando tu solicitud...'})}\n\n"
            await asyncio.sleep(0.8)

            decision = await emma_thinks(request.message)

            seo_response = ""
            content_response = ""

            # PASO 2: Delegaciones
            if decision in ["SEO", "BOTH"]:
                yield f"data: {json.dumps({'type': 'delegation', 'from': 'emma', 'to': 'seo', 'message': '🎯 Delegando a SEO Specialist...'})}\n\n"
                await asyncio.sleep(0.5)

                yield f"data: {json.dumps({'type': 'working', 'agent': 'seo', 'message': '⚡ SEO Specialist analizando...'})}\n\n"
                seo_response = await seo_agent(request.message)

                yield f"data: {json.dumps({'type': 'complete', 'agent': 'seo', 'message': '✅ Análisis SEO completado'})}\n\n"
                await asyncio.sleep(0.3)

            if decision in ["CONTENT", "BOTH"]:
                yield f"data: {json.dumps({'type': 'delegation', 'from': 'emma', 'to': 'content', 'message': '🎯 Delegando a Content Creator...'})}\n\n"
                await asyncio.sleep(0.5)

                yield f"data: {json.dumps({'type': 'working', 'agent': 'content', 'message': '⚡ Content Creator trabajando...'})}\n\n"
                content_response = await content_agent(request.message)

                yield f"data: {json.dumps({'type': 'complete', 'agent': 'content', 'message': '✅ Estrategia de contenido lista'})}\n\n"
                await asyncio.sleep(0.3)

            # PASO 3: Emma combina
            yield f"data: {json.dumps({'type': 'thinking', 'agent': 'emma', 'message': '📝 Emma preparando respuesta final...'})}\n\n"
            await asyncio.sleep(0.5)

            final_response = await emma_combines(request.message, seo_response, content_response)

            # PASO 4: Respuesta final
            yield f"data: {json.dumps({'type': 'final_response', 'response': final_response})}\n\n"
            yield f"data: {json.dumps({'type': 'stream_end'})}\n\n"

        except Exception as e:
            yield f"data: {json.dumps({'type': 'error', 'error': str(e)})}\n\n"

    return StreamingResponse(
        generate(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
        }
    )
