"""API endpoints for sketch-to-image functionality."""

import logging
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Form, UploadFile, File

from app.schemas.sketch import SketchToImageRequest, FrontendSketchResponse
from app.services.sketch_service import sketch_to_image_stability

logger = logging.getLogger(__name__)
router = APIRouter()


async def verify_api_key():
    """Verify API key is configured (placeholder for future authentication)."""
    # TODO: Implement proper API key verification if needed
    pass


@router.post(
    "/generate",
    response_model=FrontendSketchResponse,
    dependencies=[Depends(verify_api_key)],
    summary="Generate image from sketch",
    description="Generate an image from a sketch using Stability AI's v2beta sketch-to-image API."
)
async def generate_image_from_sketch(
    image: UploadFile = File(..., description="Sketch image file (JPEG, PNG, or WebP)"),
    prompt: str = Form(..., description="Description of what you want to see in the output image"),
    negative_prompt: Optional[str] = Form(None, description="What you do not want to see in the output image"),
    control_strength: Optional[float] = Form(0.7, description="How much influence the sketch has (0.0-1.0)"),
    seed: Optional[int] = Form(0, description="Random seed for generation (0 for random)"),
    output_format: Optional[str] = Form("png", description="Output format (jpeg, png, webp)"),
    style_preset: Optional[str] = Form(None, description="Style preset to guide the generation")
) -> FrontendSketchResponse:
    """
    Generate an image from a sketch using Stability AI v2beta API.
    
    **Supported Image Formats:** JPEG, PNG, WebP
    **Maximum File Size:** 10MB
    **Image Dimensions:** Minimum 64x64, maximum 9,437,184 pixels total
    **Aspect Ratio:** Between 1:2.5 and 2.5:1
    **Cost:** 3 credits per successful generation
    
    **Parameters:**
    - **image**: The sketch image file to process
    - **prompt**: Description of what you want to see (required)
    - **negative_prompt**: What you don't want to see (optional)
    - **control_strength**: How much the sketch influences the result (0.0-1.0, default: 0.7)
    - **seed**: Random seed for reproducible results (0 = random)
    - **output_format**: Output format ("jpeg", "png", or "webp", default: "png")
    - **style_preset**: Style to apply (optional)
    
    **Style Presets Available:**
    - enhance, 3d-model, analog-film, anime, cinematic, comic-book
    - digital-art, fantasy-art, isometric, line-art, low-poly
    - modeling-compound, neon-punk, origami, photographic, pixel-art, tile-texture
    """
    logger.info(f"Received sketch-to-image request: prompt='{prompt[:50]}...', control_strength={control_strength}")
    
    try:
        # Validar parámetros
        if not prompt or len(prompt.strip()) == 0:
            raise HTTPException(status_code=400, detail="Prompt is required")
        
        if control_strength is not None and (control_strength < 0.0 or control_strength > 1.0):
            raise HTTPException(status_code=400, detail="Control strength must be between 0.0 and 1.0")
        
        if seed is not None and (seed < 0 or seed > 4294967294):
            raise HTTPException(status_code=400, detail="Seed must be between 0 and 4294967294")
        
        if output_format not in ["jpeg", "png", "webp"]:
            raise HTTPException(status_code=400, detail="Output format must be jpeg, png, or webp")
        
        # Validar style_preset si se proporciona
        valid_styles = [
            "enhance", "3d-model", "analog-film", "anime", "cinematic", "comic-book",
            "digital-art", "fantasy-art", "isometric", "line-art", "low-poly",
            "modeling-compound", "neon-punk", "origami", "photographic", "pixel-art", "tile-texture"
        ]
        if style_preset and style_preset not in valid_styles:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid style preset. Valid options: {', '.join(valid_styles)}"
            )

        # Crear objeto de request
        request = SketchToImageRequest(
            prompt=prompt,
            negative_prompt=negative_prompt,
            control_strength=control_strength or 0.7,
            seed=seed or 0,
            output_format=output_format,
            style_preset=style_preset
        )

        # Llamar al servicio de Stability AI
        service_response = await sketch_to_image_stability(image, request)

        # Convertir respuesta a formato frontend
        image_data_url = f"data:image/{output_format};base64,{service_response.image}"

        logger.info("Sketch-to-image generation completed successfully")

        return FrontendSketchResponse(
            success=True,
            image_url=image_data_url,
            seed=service_response.seed,
            finish_reason=service_response.finish_reason,
            metadata={
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "control_strength": control_strength,
                "style_preset": style_preset,
                "output_format": output_format,
                "original_filename": image.filename
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in sketch-to-image endpoint: {str(e)}", exc_info=True)
        return FrontendSketchResponse(
            success=False,
            error=f"Failed to generate image from sketch: {str(e)}"
        )
