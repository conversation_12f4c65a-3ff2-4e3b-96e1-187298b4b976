"""API endpoints for premium buyer persona features."""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field

from app.services.avatar_service import avatar_service
from app.services.behavior_prediction_service import behavior_prediction_service
from app.services.conversation_simulator_service import conversation_simulator_service
from app.services.geographic_analysis_service import geographic_analysis_service

logger = logging.getLogger(__name__)

router = APIRouter()


# Request/Response Models
class AvatarGenerationRequest(BaseModel):
    persona_description: str = Field(..., description="Description of the buyer persona")
    style: str = Field(default="professional", description="Avatar style")
    gender: str = Field(default="neutral", description="Gender preference")
    age: int = Field(default=35, description="Age of the persona")
    ethnicity: str = Field(default="diverse", description="Ethnicity preference")


class BehaviorPredictionRequest(BaseModel):
    persona_data: Dict[str, Any] = Field(..., description="Complete buyer persona data")
    product_info: Dict[str, Any] = Field(..., description="Product/service information")
    market_context: Optional[Dict[str, Any]] = Field(None, description="Market context")


class ConversationStartRequest(BaseModel):
    persona_data: Dict[str, Any] = Field(..., description="Complete buyer persona data")
    conversation_type: str = Field(default="sales", description="Type of conversation")
    context: Optional[str] = Field(None, description="Conversation context")


class ConversationContinueRequest(BaseModel):
    conversation_id: str = Field(..., description="Conversation session ID")
    user_message: str = Field(..., description="Message from user/salesperson")
    conversation_data: Dict[str, Any] = Field(..., description="Current conversation state")


class GeographicAnalysisRequest(BaseModel):
    persona_data: Dict[str, Any] = Field(..., description="Complete buyer persona data")
    target_regions: Optional[List[str]] = Field(None, description="Target regions to analyze")


# Avatar Generation Endpoints
@router.post("/avatars/generate")
async def generate_avatar(request: AvatarGenerationRequest):
    """Generate an AI avatar for a buyer persona."""
    try:
        result = await avatar_service.generate_avatar(
            persona_description=request.persona_description,
            style=request.style,
            gender=request.gender,
            age=request.age,
            ethnicity=request.ethnicity
        )

        if result["status"] == "error":
            raise HTTPException(status_code=400, detail=result["error_message"])

        return result

    except Exception as e:
        logger.error(f"Error generating avatar: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate avatar: {str(e)}")


@router.post("/avatars/gallery")
async def generate_avatar_gallery(request: AvatarGenerationRequest):
    """Generate a gallery of avatar options for a persona."""
    try:
        gallery = await avatar_service.generate_avatar_gallery(
            persona_description=request.persona_description,
            count=6
        )

        return {
            "status": "success",
            "gallery": gallery,
            "total_avatars": len(gallery)
        }

    except Exception as e:
        logger.error(f"Error generating avatar gallery: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate avatar gallery: {str(e)}")


@router.get("/avatars/styles")
async def get_avatar_styles():
    """Get available avatar styles."""
    try:
        styles = await avatar_service.get_avatar_styles()
        return {
            "status": "success",
            "styles": styles
        }

    except Exception as e:
        logger.error(f"Error getting avatar styles: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get avatar styles: {str(e)}")


# Behavior Prediction Endpoints
@router.post("/behavior/predict")
async def predict_buyer_behavior(request: BehaviorPredictionRequest):
    """Predict comprehensive buyer behavior for a persona."""
    try:
        result = await behavior_prediction_service.predict_buyer_behavior(
            persona_data=request.persona_data,
            product_info=request.product_info,
            market_context=request.market_context
        )

        if result["status"] == "error":
            raise HTTPException(status_code=400, detail=result["error_message"])

        return result

    except Exception as e:
        logger.error(f"Error predicting behavior: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to predict behavior: {str(e)}")


# Conversation Simulator Endpoints
@router.post("/conversation/start")
async def start_conversation(request: ConversationStartRequest):
    """Start a new conversation simulation with a buyer persona."""
    try:
        result = await conversation_simulator_service.start_conversation(
            persona_data=request.persona_data,
            conversation_type=request.conversation_type,
            context=request.context
        )

        if result["status"] == "error":
            raise HTTPException(status_code=400, detail=result["error_message"])

        return result

    except Exception as e:
        logger.error(f"Error starting conversation: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start conversation: {str(e)}")


@router.post("/conversation/continue")
async def continue_conversation(request: ConversationContinueRequest):
    """Continue an existing conversation with a persona response."""
    try:
        result = await conversation_simulator_service.continue_conversation(
            conversation_id=request.conversation_id,
            user_message=request.user_message,
            conversation_data=request.conversation_data
        )

        if result["status"] == "error":
            raise HTTPException(status_code=400, detail=result["error_message"])

        return result

    except Exception as e:
        logger.error(f"Error continuing conversation: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to continue conversation: {str(e)}")

@router.get("/conversation/history/{persona_name}")
async def get_conversation_history(persona_name: str):
    """Get conversation history for a specific persona."""
    try:
        conversations = conversation_simulator_service.get_conversation_history(persona_name)
        return {
            "status": "success",
            "conversations": conversations,
            "total": len(conversations)
        }
    except Exception as e:
        logger.error(f"Error getting conversation history: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting conversation history: {str(e)}")

@router.get("/conversation/{conversation_id}")
async def get_conversation(conversation_id: str):
    """Get a specific conversation by ID."""
    try:
        conversation = conversation_simulator_service._load_conversation(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")

        return {
            "status": "success",
            "conversation": conversation
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting conversation: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting conversation: {str(e)}")


# Geographic Analysis Endpoints
@router.post("/geographic/analyze")
async def analyze_geographic_factors(request: GeographicAnalysisRequest):
    """Analyze geographic and cultural factors for buyer personas."""
    try:
        result = await geographic_analysis_service.analyze_geographic_factors(
            persona_data=request.persona_data,
            target_regions=request.target_regions
        )

        if result["status"] == "error":
            raise HTTPException(status_code=400, detail=result["error_message"])

        return result

    except Exception as e:
        logger.error(f"Error analyzing geographic factors: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to analyze geographic factors: {str(e)}")


# Combined Premium Analysis Endpoint
@router.post("/premium/complete-analysis")
async def complete_premium_analysis(
    background_tasks: BackgroundTasks,
    persona_data: Dict[str, Any],
    product_info: Dict[str, Any],
    target_regions: Optional[List[str]] = None,
    generate_avatar: bool = True,
    predict_behavior: bool = True,
    analyze_geography: bool = True
):
    """
    Perform complete premium analysis including avatars, behavior prediction, and geographic analysis.
    """
    try:
        results = {
            "status": "success",
            "persona_name": persona_data.get("name", "Unknown"),
            "analysis_timestamp": "",
            "avatar_data": None,
            "behavior_predictions": None,
            "geographic_analysis": None
        }

        # Generate avatar if requested
        if generate_avatar:
            try:
                avatar_result = await avatar_service.generate_avatar(
                    persona_description=persona_data.get("personal_background", "Professional persona"),
                    style="professional",
                    gender=persona_data.get("gender", "neutral"),
                    age=persona_data.get("age", 35)
                )
                if avatar_result["status"] == "success":
                    results["avatar_data"] = avatar_result
            except Exception as e:
                logger.warning(f"Avatar generation failed: {e}")
                results["avatar_data"] = {"status": "error", "error": str(e)}

        # Predict behavior if requested
        if predict_behavior:
            try:
                behavior_result = await behavior_prediction_service.predict_buyer_behavior(
                    persona_data=persona_data,
                    product_info=product_info
                )
                if behavior_result["status"] == "success":
                    results["behavior_predictions"] = behavior_result
            except Exception as e:
                logger.warning(f"Behavior prediction failed: {e}")
                results["behavior_predictions"] = {"status": "error", "error": str(e)}

        # Analyze geography if requested
        if analyze_geography:
            try:
                geo_result = await geographic_analysis_service.analyze_geographic_factors(
                    persona_data=persona_data,
                    target_regions=target_regions
                )
                if geo_result["status"] == "success":
                    results["geographic_analysis"] = geo_result
            except Exception as e:
                logger.warning(f"Geographic analysis failed: {e}")
                results["geographic_analysis"] = {"status": "error", "error": str(e)}

        # Set timestamp
        from datetime import datetime
        results["analysis_timestamp"] = datetime.now().isoformat()

        return results

    except Exception as e:
        logger.error(f"Error in complete premium analysis: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to complete premium analysis: {str(e)}")


# Health Check for Premium Services
@router.get("/premium/health")
async def premium_services_health():
    """Check health status of all premium services."""
    try:
        health_status = {
            "status": "healthy",
            "services": {
                "avatar_service": "healthy",
                "behavior_prediction_service": "healthy",
                "conversation_simulator_service": "healthy",
                "geographic_analysis_service": "healthy"
            },
            "ai_services": {
                "gemini_ai": "available" if avatar_service.gemini_model else "unavailable"
            },
            "timestamp": ""
        }

        from datetime import datetime
        health_status["timestamp"] = datetime.now().isoformat()

        return health_status

    except Exception as e:
        logger.error(f"Error checking premium services health: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


# Premium Features Info Endpoint
@router.get("/premium/features")
async def get_premium_features():
    """Get information about available premium features."""
    return {
        "status": "success",
        "features": {
            "ai_avatars": {
                "name": "Avatares AI Realistas",
                "description": "Genera avatares realistas para cada buyer persona",
                "capabilities": [
                    "Múltiples estilos (profesional, casual, creativo, ejecutivo)",
                    "Personalización por edad, género y etnia",
                    "Galería de opciones",
                    "Exportación en alta resolución"
                ],
                "endpoint": "/api/v1/premium/avatars/generate"
            },
            "behavior_prediction": {
                "name": "Predicción de Comportamiento",
                "description": "Predice patrones de compra y comportamiento del cliente",
                "capabilities": [
                    "Probabilidad de compra",
                    "Canales de conversión más efectivos",
                    "Timing óptimo para contacto",
                    "Sensibilidad al precio",
                    "Objeciones probables"
                ],
                "endpoint": "/api/v1/premium/behavior/predict"
            },
            "conversation_simulator": {
                "name": "Simulador de Conversaciones",
                "description": "Simula conversaciones realistas con buyer personas",
                "capabilities": [
                    "Chat simulado basado en perfil",
                    "Múltiples tipos de conversación",
                    "Análisis de sentimiento en tiempo real",
                    "Entrenamiento de ventas",
                    "A/B testing de mensajes"
                ],
                "endpoint": "/api/v1/premium/conversation/start"
            },
            "geographic_analysis": {
                "name": "Análisis Geográfico y Cultural",
                "description": "Analiza factores culturales y geográficos por región",
                "capabilities": [
                    "Adaptación por países/regiones",
                    "Diferencias culturales",
                    "Horarios óptimos por zona",
                    "Canales preferidos por región",
                    "Compliance local"
                ],
                "endpoint": "/api/v1/premium/geographic/analyze"
            }
        },
        "pricing_tiers": {
            "free": {
                "features": ["Buyer personas básicas", "Export PDF"],
                "limits": "3 personas por mes"
            },
            "pro": {
                "features": ["Todo lo anterior", "Avatares AI", "Predicción básica"],
                "limits": "Personas ilimitadas",
                "price": "$29/mes"
            },
            "enterprise": {
                "features": ["Todo lo anterior", "Simulador", "Análisis geográfico", "API access"],
                "limits": "Sin límites",
                "price": "$199/mes"
            }
        }
    }
