"""
API endpoints for poster generation and editing using OpenAI's gpt-image-1.
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, Form, UploadFile, File
from fastapi.responses import StreamingResponse
from typing import Optional, List
import json

from app.core.auth import verify_api_key
from app.services.poster_service import poster_service
from app.schemas.poster import (
    PosterGenerationRequest,
    FrontendPosterResponse,
    FrontendStreamResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post(
    "/generate",
    response_model=FrontendPosterResponse,
    dependencies=[Depends(verify_api_key)],
)
async def generate_poster(
    prompt: str = Form(..., description="Description of the poster to create"),
    size: str = Form(default="auto", description="Image size (1024x1024, 1536x1024, 1024x1536, auto)")
) -> FrontendPosterResponse:
    """Generate a poster using OpenAI's gpt-image-1 model."""
    
    try:
        logger.info(f"🎨 Generating poster: {prompt[:100]}...")
        
        # Call the service
        service_response = await poster_service.generate_poster(
            prompt=prompt,
            size=size
        )
        
        # Convert to frontend response
        return FrontendPosterResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in generate_poster endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during poster generation: {e}"
        )


@router.post(
    "/multi-turn-edit",
    response_model=FrontendPosterResponse,
    dependencies=[Depends(verify_api_key)],
)
async def multi_turn_edit(
    previous_response_id: str = Form(..., description="ID of the previous response to build upon"),
    edit_prompt: str = Form(..., description="Description of the changes to make")
) -> FrontendPosterResponse:
    """Edit an existing poster using multi-turn generation."""
    
    try:
        logger.info(f"🔄 Multi-turn editing: {edit_prompt[:100]}...")
        
        # Call the service
        service_response = await poster_service.multi_turn_edit(
            previous_response_id=previous_response_id,
            edit_prompt=edit_prompt
        )
        
        # Convert to frontend response
        return FrontendPosterResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in multi_turn_edit endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during multi-turn edit: {e}"
        )


@router.post(
    "/stream-generate",
    dependencies=[Depends(verify_api_key)],
)
async def stream_generate_poster(
    prompt: str = Form(..., description="Description of the poster to create")
):
    """Generate a poster with streaming partial images."""
    
    try:
        logger.info(f"🌊 Streaming poster generation: {prompt[:100]}...")
        
        async def generate_stream():
            async for chunk in poster_service.stream_generation(prompt):
                # Convert to frontend response format
                frontend_response = FrontendStreamResponse(
                    success=chunk.get("success", False),
                    partial_image=chunk.get("partial_image"),
                    image_url=chunk.get("image_url"),
                    index=chunk.get("index"),
                    progress=chunk.get("progress"),
                    error=chunk.get("error")
                )
                
                # Send as Server-Sent Events format
                yield f"data: {frontend_response.model_dump_json()}\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )
        
    except Exception as e:
        logger.error(f"Error in stream_generate_poster endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during streaming generation: {e}"
        )


@router.post(
    "/edit-with-references",
    response_model=FrontendPosterResponse,
    dependencies=[Depends(verify_api_key)],
)
async def edit_with_references(
    prompt: str = Form(..., description="Description of the poster to create"),
    size: str = Form(default="auto", description="Image size (1024x1024, 1536x1024, 1024x1536, auto)"),
    reference_images: List[UploadFile] = File(..., description="Reference images to use")
) -> FrontendPosterResponse:
    """Generate poster using reference images."""
    
    try:
        logger.info(f"🖼️ Generating with {len(reference_images)} reference images: {prompt[:100]}...")
        
        # Validate reference images
        if len(reference_images) > 4:
            raise HTTPException(
                status_code=400,
                detail="Maximum 4 reference images allowed"
            )
        
        for ref_image in reference_images:
            if not ref_image.content_type or not ref_image.content_type.startswith("image/"):
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid file type: {ref_image.content_type}. Only images are allowed."
                )
        
        # Call the service
        service_response = await poster_service.edit_with_references(
            prompt=prompt,
            reference_images=reference_images,
            size=size
        )
        
        # Convert to frontend response
        return FrontendPosterResponse.from_service_response(service_response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in edit_with_references endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during reference edit: {e}"
        )


@router.post(
    "/edit-with-mask",
    response_model=FrontendPosterResponse,
    dependencies=[Depends(verify_api_key)],
)
async def edit_with_mask(
    prompt: str = Form(..., description="Description of what to put in the masked area"),
    image: UploadFile = File(..., description="The original image to edit"),
    mask: UploadFile = File(..., description="The mask image (white areas will be edited)")
) -> FrontendPosterResponse:
    """Edit poster using a mask to specify areas to change."""
    
    try:
        logger.info(f"✏️ Editing with mask: {prompt[:100]}...")
        
        # Validate file types
        if not image.content_type or not image.content_type.startswith("image/"):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid image file type: {image.content_type}. Only images are allowed."
            )
        
        if not mask.content_type or not mask.content_type.startswith("image/"):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid mask file type: {mask.content_type}. Only images are allowed."
            )
        
        # Call the service
        service_response = await poster_service.edit_with_mask(
            image=image,
            mask=mask,
            prompt=prompt
        )
        
        # Convert to frontend response
        return FrontendPosterResponse.from_service_response(service_response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in edit_with_mask endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during mask edit: {e}"
        )
