"""API endpoints for erasing objects from images using Stability AI v2beta API."""

import logging
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Form, UploadFile, File

from app.schemas.erase import EraseRequest, FrontendEraseRequest, FrontendEraseResponse
from app.services.erase_service import erase_objects_stability
from app.core.auth import verify_api_key

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post(
    "/erase",
    response_model=FrontendEraseResponse,
    dependencies=[Depends(verify_api_key)],
    summary="Erase objects from image",
    description="""
    Erase objects from an image using Stability AI v2beta API.
    
    **Supported Image Formats:** JPEG, PNG, WebP
    **Maximum File Size:** 10MB
    **Image Dimensions:** Minimum 64x64, maximum 9,437,184 pixels total
    **Cost:** 3 credits per successful generation
    
    **Parameters:**
    - **image**: The image file to process (required)
    - **mask**: Optional mask file indicating areas to erase (black = preserve, white = erase)
    - **grow_mask**: Grows the edges of the mask outward (0-20 pixels, default: 5)
    - **seed**: Random seed for reproducible results (0 = random, default: 0)
    - **output_format**: Output format ("jpeg", "png", or "webp", default: "webp")
    
    **Mask Options:**
    1. **Explicit mask**: Pass a separate black and white image via the `mask` parameter
    2. **Alpha channel**: Use an image with transparency (alpha channel) as the mask
    
    **How masks work:**
    - **Black pixels**: Areas to preserve (no erasing)
    - **White pixels**: Areas to erase completely
    - **Gray pixels**: Partial erasing based on intensity
    """
)
async def erase_objects_endpoint(
    image: UploadFile = File(..., description="Image file to process"),
    mask: Optional[UploadFile] = File(None, description="Optional mask file (black=preserve, white=erase)"),
    grow_mask: Optional[int] = Form(5, description="Grow mask edges (0-20 pixels)"),
    seed: Optional[int] = Form(0, description="Random seed (0 = random)"),
    output_format: Optional[str] = Form("webp", description="Output format (jpeg, png, webp)")
) -> FrontendEraseResponse:
    """
    Erase objects from an image using Stability AI v2beta API.
    
    This endpoint removes unwanted objects from images using either:
    1. An explicit mask image (black and white)
    2. The alpha channel of the input image
    
    The mask indicates which areas to erase:
    - Black pixels = preserve the area
    - White pixels = erase the area completely
    - Gray pixels = partial erasing
    """
    try:
        logger.info(f"Received erase request for file: {image.filename}")
        logger.info(f"Has mask: {mask is not None}")
        if mask:
            logger.info(f"Mask filename: {mask.filename}")
        
        # Validar parámetros
        if grow_mask is not None and (grow_mask < 0 or grow_mask > 20):
            raise HTTPException(
                status_code=400,
                detail="grow_mask must be between 0 and 20"
            )
        
        if seed is not None and (seed < 0 or seed > 4294967294):
            raise HTTPException(
                status_code=400,
                detail="seed must be between 0 and 4294967294"
            )
        
        if output_format not in ["jpeg", "png", "webp"]:
            raise HTTPException(
                status_code=400,
                detail="output_format must be 'jpeg', 'png', or 'webp'"
            )

        # Crear request object
        erase_request = EraseRequest(
            grow_mask=grow_mask or 5,
            seed=seed or 0,
            output_format=output_format or "webp"
        )

        # Llamar al servicio de Stability AI
        service_response = await erase_objects_stability(
            image_file=image,
            mask_file=mask,
            request=erase_request
        )

        # Convertir respuesta a formato frontend
        image_data_url = f"data:image/{output_format};base64,{service_response.image}"

        logger.info("Erase operation completed successfully")

        return FrontendEraseResponse(
            success=True,
            image_url=image_data_url,
            metadata={
                "grow_mask": grow_mask,
                "seed": service_response.seed,
                "output_format": output_format,
                "finish_reason": service_response.finish_reason,
                "original_filename": image.filename,
                "mask_filename": mask.filename if mask else None,
                "has_mask": mask is not None
            }
        )

    except HTTPException as http_exc:
        # Re-raise HTTP exceptions
        logger.error(f"HTTP error in erase operation: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logger.error(f"Unexpected error in erase operation: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during erase operation: {str(e)}"
        )
