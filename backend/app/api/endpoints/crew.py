"""
Crew API endpoints - REAL AGENTS ONLY
Uses the actual agent system instead of simulations.
"""
import asyncio
import json
import time
from typing import Dict, Any
import logging

from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.crew import AgentChatRequest

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/chat/stream")
async def chat_with_real_agents(
    request: AgentChatRequest,
    req: Request,
    db: Session = Depends(get_db)
):
    """
    Chat with REAL AGENTS - Uses the actual agent system.
    Emma coordinates with real SEO and Content agents using LLMs.
    """
    from app.models.relevance_events import (
        create_session, create_delegation_event, StreamEvent
    )
    from app.services.agent_service import agent_service

    request_id = req.headers.get("X-Request-ID", f"req_{int(time.time()*1000)}")
    logger.info(f"Processing REAL AGENT chat: {request_id}")

    async def generate_stream():
        try:
            # Create session for tracking
            session = create_session(request_id, request.message)

            # Get the REAL Emma agent
            emma_agent = agent_service.config.orchestrator.agents.get('emma')
            if not emma_agent:
                yield f"data: {json.dumps({'type': 'error', 'error_message': 'Emma agent not found', 'timestamp': time.time()})}\n\n"
                return

            logger.info(f"Using REAL Emma agent: {emma_agent.name}")

            # Create task and context for Emma using real agent protocol types
            from agents.protocols.agent_protocol_types import AgentTask
            from agents.base_agent import AgentContext, ContextType

            task = AgentTask(
                id=request_id,
                description=request.message,
                priority=1,  # High priority (1-10 scale)
                metadata={"request_id": request_id, "user_message": request.message}
            )

            # Create context for Emma (this is what she expects)
            context = AgentContext(
                context_type=ContextType.TASK,
                data={"task": task}
            )

            # Emma makes autonomous decision
            action = await emma_agent.get_next_action(context)
            logger.info(f"Emma's autonomous decision: {action.type}")

            # Execute Emma's action and get REAL responses
            if action.type == "COORDINATE_MULTI_AGENT":
                # Emma is coordinating multiple agents - REAL delegation

                # Create delegations for tracking
                content_delegation = create_delegation_event(
                    request_id, 'emma', 'content',
                    f'Create content strategy for: {request.message}'
                )
                seo_delegation = create_delegation_event(
                    request_id, 'emma', 'seo',
                    f'Optimize SEO for: {request.message}'
                )

                # Send delegation events
                logger.info(f"🚀 SENDING delegation_start event for content: {content_delegation.id}")
                yield f"data: {json.dumps(StreamEvent(type='delegation_start', delegation=content_delegation, timestamp=time.time()).model_dump())}\n\n"
                await asyncio.sleep(0.1)
                logger.info(f"🚀 SENDING delegation_start event for seo: {seo_delegation.id}")
                yield f"data: {json.dumps(StreamEvent(type='delegation_start', delegation=seo_delegation, timestamp=time.time()).model_dump())}\n\n"
                await asyncio.sleep(0.1)

                # Execute REAL coordination with REAL agents
                result = await emma_agent.execute_action(action)

                if result.success:
                    # Mark delegations as completed
                    content_delegation.status = 'completed'
                    content_delegation.completed_at = time.time()
                    seo_delegation.status = 'completed'
                    seo_delegation.completed_at = time.time()

                    # Send completion events
                    logger.info(f"✅ SENDING delegation_complete event for content: {content_delegation.id}")
                    yield f"data: {json.dumps(StreamEvent(type='delegation_complete', delegation=content_delegation, timestamp=time.time()).model_dump())}\n\n"
                    logger.info(f"✅ SENDING delegation_complete event for seo: {seo_delegation.id}")
                    yield f"data: {json.dumps(StreamEvent(type='delegation_complete', delegation=seo_delegation, timestamp=time.time()).model_dump())}\n\n"

                    # Send final response from REAL Emma
                    final_response = result.result
                else:
                    final_response = f"Error: {result.error}"

            elif action.type in ["DELEGATE_TO_SEO", "DELEGATE_TO_CONTENT"]:
                # Emma is delegating to a single specialist - REAL delegation

                # Determine target agent
                target_agent = "seo" if action.type == "DELEGATE_TO_SEO" else "content"
                agent_name = "SEO Specialist" if target_agent == "seo" else "Content Creator"

                # Create delegation for tracking
                delegation = create_delegation_event(
                    request_id, 'emma', target_agent,
                    f'Specialist task for: {request.message}'
                )

                # Send delegation start event
                logger.info(f"🚀 SENDING delegation_start event for {target_agent}: {delegation.id}")
                yield f"data: {json.dumps(StreamEvent(type='delegation_start', delegation=delegation, timestamp=time.time()).model_dump())}\n\n"
                await asyncio.sleep(0.1)

                # Execute REAL delegation with REAL agent
                result = await emma_agent.execute_action(action)

                if result.success:
                    # Mark delegation as completed
                    delegation.status = 'completed'
                    delegation.completed_at = time.time()

                    # Send completion event
                    logger.info(f"✅ SENDING delegation_complete event for {target_agent}: {delegation.id}")
                    yield f"data: {json.dumps(StreamEvent(type='delegation_complete', delegation=delegation, timestamp=time.time()).model_dump())}\n\n"

                    # Send final response from REAL Emma
                    final_response = result.result
                else:
                    final_response = f"Error: {result.error}"

            elif action.type == "PROCESS":
                # Emma handles this directly
                result = await emma_agent.execute_action(action)
                final_response = result.result if result.success else f"Error: {result.error}"

            else:
                # Other action types
                result = await emma_agent.execute_action(action)
                final_response = result.result if result.success else f"Error: {result.error}"

            # Extract string response from result
            if isinstance(final_response, dict):
                # If it's a dict, extract the actual response string
                if 'result' in final_response:
                    response_text = final_response['result']
                elif 'response' in final_response:
                    response_text = final_response['response']
                else:
                    response_text = str(final_response)
            else:
                response_text = str(final_response)

            # Send final response from Emma
            session.final_response = response_text
            session.is_complete = True

            # Send final response event
            event = StreamEvent(
                type='final_response',
                response=response_text,
                timestamp=time.time()
            )
            yield f"data: {json.dumps(event.model_dump())}\n\n"

            # Send completion event
            event = StreamEvent(
                type='session_complete',
                session_id=request_id,
                timestamp=time.time()
            )
            yield f"data: {json.dumps(event.model_dump())}\n\n"

            # Send stream end event
            event = StreamEvent(
                type='stream_end',
                timestamp=time.time()
            )
            yield f"data: {json.dumps(event.model_dump())}\n\n"

        except Exception as e:
            logger.error(f"Error in streaming chat: {str(e)}", exc_info=True)
            yield f"data: {json.dumps({'type': 'error', 'error_message': f'Error: {str(e)}', 'timestamp': time.time()})}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Content-Type, X-Request-ID",
        }
    )


@router.get("/session/{session_id}/conversation/{conversation_id}")
async def get_conversation(session_id: str, conversation_id: str):
    """
    Get a specific conversation between agents - RELEVANCE AI STYLE.
    This is what happens when user clicks "View conversation".
    """
    from app.models.relevance_events import get_session

    session = get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    conversation = session.conversations.get(conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")

    return {
        "conversation_id": conversation.id,
        "agent_from": conversation.agent_from,
        "agent_to": conversation.agent_to,
        "messages": [
            {
                "id": msg.id,
                "from": msg.from_agent,
                "to": msg.to_agent,
                "content": msg.content,
                "timestamp": msg.timestamp
            }
            for msg in conversation.messages
        ],
        "created_at": conversation.created_at,
        "updated_at": conversation.updated_at
    }


@router.get("/session/{session_id}/delegations")
async def get_session_delegations(session_id: str):
    """
    Get all delegations for a session - RELEVANCE AI STYLE.
    This shows the tracings section.
    """
    from app.models.relevance_events import get_session

    session = get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    return {
        "session_id": session.id,
        "user_message": session.user_message,
        "delegations": [
            {
                "id": delegation.id,
                "agent_from": delegation.agent_from,
                "agent_to": delegation.agent_to,
                "task_description": delegation.task_description,
                "status": delegation.status,
                "started_at": delegation.started_at,
                "completed_at": delegation.completed_at,
                "conversation_id": f"conv_{delegation.agent_from}_{delegation.agent_to}"
            }
            for delegation in session.delegations.values()
        ],
        "final_response": session.final_response,
        "created_at": session.created_at,
        "completed_at": session.completed_at
    }
