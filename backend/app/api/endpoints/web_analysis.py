"""API endpoints for website analysis using Jina AI + Gemini."""

import logging
from typing import Dict, Any
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, HttpUrl

from app.services.web_analyzer import web_analyzer

logger = logging.getLogger(__name__)
router = APIRouter()

class WebAnalysisRequest(BaseModel):
    """Request model for website analysis"""
    url: HttpUrl
    
class WebAnalysisResponse(BaseModel):
    """Response model for website analysis"""
    status: str
    url: str
    brand_info: Dict[str, Any]
    extracted_content_length: int = 0
    error: str = None

@router.post("/analyze-website", response_model=WebAnalysisResponse)
async def analyze_website(request: WebAnalysisRequest) -> WebAnalysisResponse:
    """
    Analyze a website and extract brand information using Jina AI Reader + Gemini.
    
    This endpoint:
    1. Uses Jina AI Reader to extract clean content from the website
    2. Analyzes the content with Gemini to extract brand information
    3. Returns structured brand data for the post generator
    
    Args:
        request: Contains the URL to analyze
        
    Returns:
        Brand information including business name, industry, voice tone, colors, etc.
    """
    try:
        logger.info(f"Starting website analysis for: {request.url}")
        
        # Analyze the website
        result = await web_analyzer.analyze_website(str(request.url))
        
        if result["status"] == "error":
            raise HTTPException(
                status_code=400,
                detail=f"Website analysis failed: {result['error']}"
            )
        
        logger.info(f"Successfully analyzed website: {request.url}")
        
        return WebAnalysisResponse(
            status=result["status"],
            url=result["url"],
            brand_info=result["brand_info"],
            extracted_content_length=result.get("extracted_content_length", 0)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error analyzing website {request.url}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during website analysis: {str(e)}"
        )

@router.get("/test-jina")
async def test_jina_connection():
    """Test endpoint to verify Jina AI Reader connectivity"""
    try:
        # Test with a simple, reliable website
        test_url = "https://example.com"
        result = await web_analyzer.analyze_website(test_url)
        
        return {
            "status": "success",
            "message": "Jina AI Reader connection test successful",
            "test_url": test_url,
            "content_extracted": result["status"] == "success",
            "content_length": result.get("extracted_content_length", 0)
        }
        
    except Exception as e:
        logger.error(f"Jina connection test failed: {str(e)}")
        return {
            "status": "error", 
            "message": f"Jina AI Reader connection test failed: {str(e)}"
        }
