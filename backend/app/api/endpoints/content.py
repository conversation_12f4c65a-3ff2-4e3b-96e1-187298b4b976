"""API endpoints for content generation and history."""

# Standard library imports first
import logging
import time
from typing import Optional, List, Dict, Any

# Third-party imports
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import requests
import requests.exceptions

# Local application imports
from app.core.config import settings
from app.schemas.content import (
    GenerateContentRequest,
    GenerateContentResponse, ImprovePromptRequest,
    ImprovePromptResponse
)
from app.services.content_service import (
    generate_content_service,
    get_history_service,
    get_history_count_service,
    get_prompt_optimization_history_service,
    get_trace_service,
    improve_prompt_service
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/generate", response_model=GenerateContentResponse)
async def generate_content(req: GenerateContentRequest, request: Request) -> GenerateContentResponse:
    """
    Generates content based on the request using the service layer.

    Args:
        req: The content generation request with prompt and parameters
        request: The FastAPI request object

    Returns:
        Generated content response

    Raises:
        HTTPException: If the request is invalid or content generation fails
    """
    from app.utils.validation import validate_string

    # Get or generate request ID
    request_id = request.headers.get("X-Request-ID", f"req_{int(time.time()*1000)}")

    # Validate and sanitize prompt
    try:
        sanitized_prompt = validate_string(
            req.prompt,
            min_length=3,
            max_length=5000,
            sanitize=True
        )

        # Update the request with sanitized prompt
        req.prompt = sanitized_prompt
    except ValueError as e:
        logger.warning(
            "Invalid prompt in content generation request. RequestID: %s, Error: %s",
            request_id, str(e)
        )
        raise HTTPException(
            status_code=400,
            detail={
                "request_id": request_id,
                "error": {
                    "code": "invalid_prompt",
                    "message": str(e),
                },
            },
        )

    logger.info(
        "Received request to generate content. RequestID: %s, Prompt: %s",
        request_id, req.prompt
    )
    try:
        generated_content = await generate_content_service(req)
        logger.info(
            "Content generation successful. RequestID: %s", request_id
        )
        return generated_content
    except requests.exceptions.RequestException as req_exc:
        logger.error(
            "HTTP request error during content generation for RequestID %s: %s",
            request_id, req_exc, exc_info=True
        )
        raise HTTPException(
            status_code=503,
            detail={
                "request_id": request_id,
                "error": {
                    "code": "external_service_error",
                    "message": f"Error communicating with external service: {req_exc}",
                },
            },
        ) from req_exc
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.error(
            "Unexpected error during content generation (%s) for RequestID %s: %s",
            type(e).__name__, request_id, e, exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail={
                "request_id": request_id,
                "error": {
                    "code": "internal_server_error",
                    "message": f"An unexpected error occurred during content generation: {e}",
                },
            },
        ) from e


@router.post("/improve-prompt", response_model=ImprovePromptResponse)
async def improve_prompt(req: ImprovePromptRequest, request: Request) -> ImprovePromptResponse:
    """Improves a given prompt using Google Gemini via the service layer."""
    request_id = request.headers.get("X-Request-ID", "unknown")
    logger.info(
        "Received request to improve prompt. RequestID: %s", request_id
    )
    try:
        service_result = await improve_prompt_service(req)

        # Log the optimization event (assuming success)
        # optimization_log = prompt_optimization_log.log_prompt_optimization(
        #     original_prompt=req.prompt,
        #     improved_prompt=service_result.improved_prompt,
        #     model_used=service_result.model_used,
        #     user_id=req.user_id,
        #     session_id=req.session_id,
        #     request_id=request_id
        # )
        # logger.info("Prompt optimization logged. Log ID: %s RequestID: %s", optimization_log.id, request_id)

        # Log optimization details (if function existed)
        try:
            # prompt_optimization_log(
            #     user_id="dummy_user", # Replace with actual user ID if available
            #     original_prompt=req.prompt,
            #     improved_prompt=service_result["improved_prompt"],
            #     model_used="gemini-pro", # Corrected based on service
            #     latency_ms=service_result["latency_ms"],
            #     reasoning=service_result["reasoning"]
            # )
            pass
        except Exception as log_e:
            logger.error(f"Failed to log prompt optimization: {log_e}", exc_info=True)

        return ImprovePromptResponse(
            original_prompt=req.prompt,
            improved_prompt=service_result["improved_prompt"] # Access verified
        )

    except requests.exceptions.RequestException as http_err:
        logger.error(
            "HTTP request error during prompt improvement for RequestID %s: %s",
            request_id, http_err, exc_info=True
        )
        raise HTTPException(
            status_code=503,
            detail={
                "request_id": request_id,
                "error": {
                    "code": "external_service_error",
                    "message": f"Error communicating with external service: {http_err}",
                },
            },
        ) from http_err
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.error(
            "Error during prompt improvement (%s) for RequestID %s: %s",
            type(e).__name__, request_id, e, exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail={
                "request_id": request_id,
                "error": {
                    "code": "internal_server_error",
                    "message": f"An unexpected error occurred during prompt improvement: {e}",
                },
            },
        ) from e


# --- History Endpoints ---


# Path: /api/v1/content/history
@router.get("/history")
async def content_history(
    user_id: Optional[str] = None,
    limit: int = 10,
    offset: int = 0
) -> Dict[str, Any]:
    """
    Retrieves the content generation history with pagination.

    Args:
        user_id: Optional user ID to filter results
        limit: Maximum number of items to return (default: 10)
        offset: Number of items to skip for pagination (default: 0)

    Returns:
        Dictionary with items and pagination metadata
    """
    try:
        # Apply pagination parameters
        history_service = await get_history_service(limit=limit, offset=offset, user_id=user_id)

        # Get total count for pagination metadata
        total_count = await get_history_count_service(user_id=user_id)

        logger.info(
            "Content history retrieved successfully. UserID: %s, Limit: %d, Offset: %d, Total: %d",
            user_id, limit, offset, total_count
        )

        # Return paginated response with metadata
        return {
            "items": history_service,
            "pagination": {
                "total": total_count,
                "limit": limit,
                "offset": offset,
                "has_more": offset + len(history_service) < total_count
            }
        }
    except requests.exceptions.RequestException as req_exc:
        logger.error(
            "HTTP request error fetching history for RequestID %s: %s",
            user_id, req_exc, exc_info=True
        )
        raise HTTPException(
            status_code=503,
            detail={
                "request_id": user_id,
                "error": {
                    "code": "external_service_error",
                    "message": f"Error communicating with history service: {req_exc}",
                },
            },
        ) from req_exc
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.error(
            "Error fetching content history (%s) for RequestID %s: %s",
            type(e).__name__, user_id, e, exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail={
                "request_id": user_id,
                "error": {
                    "code": "internal_server_error",
                    "message": f"Failed to retrieve history: {e}",
                },
            },
        ) from e


# Path: /api/v1/content/trace/{trace_id}
@router.get("/trace/{trace_id}")
async def content_trace(trace_id: str) -> List[Dict[str, Any]]:
    """Retrieves a specific reasoning trace by its ID."""
    logger.info("Attempting to retrieve trace with ID: %s", trace_id)
    try:
        trace = await get_trace_service(trace_id)
        if not trace:
            raise HTTPException(status_code=404, detail="Trace not found")
        return trace
    except requests.exceptions.RequestException as req_exc:
        logger.error(
            "HTTP request error fetching trace for RequestID %s, TraceID %s: %s",
            trace_id, trace_id, req_exc, exc_info=True
        )
        raise HTTPException(
            status_code=503,
            detail={
                "request_id": trace_id,
                "error": {
                    "code": "external_service_error",
                    "message": f"Error communicating with trace service: {req_exc}",
                },
            },
        ) from req_exc
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.error(
            "Error fetching trace (%s) for RequestID %s, TraceID %s: %s",
            type(e).__name__, trace_id, trace_id, e, exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail={
                "request_id": trace_id,
                "error": {
                    "code": "internal_server_error",
                    "message": f"Failed to retrieve trace: {e}",
                },
            },
        ) from e


# Path: /api/v1/content/prompt/optimization-history
@router.get("/prompt/optimization-history")
async def prompt_optimization_history(user_id: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
    """Retrieves the prompt optimization history, optionally filtered by user_id."""
    try:
        prompt_history = await get_prompt_optimization_history_service()
        logger.info(
            "Prompt optimization history retrieved. UserID (filter): %s, Limit: %d",
            user_id, limit
        )
        return prompt_history
    except requests.exceptions.RequestException as req_exc:
        logger.error(
            "HTTP request error fetching prompt optimization history for RequestID %s: %s",
            user_id, req_exc, exc_info=True
        )
        raise HTTPException(
            status_code=503,
            detail={
                "request_id": user_id,
                "error": {
                    "code": "external_service_error",
                    "message": f"Error communicating with history service: {req_exc}",
                },
            },
        ) from req_exc
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.error(
            "Error fetching prompt optimization history (%s) for RequestID %s: %s",
             type(e).__name__, user_id, e, exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail={
                "request_id": user_id,
                "error": {
                    "code": "internal_server_error",
                    "message": f"Failed to retrieve prompt optimization history: {e}",
                },
            },
        ) from e
