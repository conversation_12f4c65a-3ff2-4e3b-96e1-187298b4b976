"""Add SEO analyses table for persistent analysis tracking

Revision ID: add_seo_analyses_table
Revises: 
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_seo_analyses_table'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # Create seo_analyses table
    op.create_table(
        'seo_analyses',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('analysis_id', sa.String(length=64), nullable=False),
        sa.Column('user_id', sa.String(length=64), nullable=True),
        sa.Column('url', sa.String(length=512), nullable=False),
        sa.Column('mode', sa.String(length=32), nullable=False),
        sa.Column('status', sa.String(length=32), nullable=False),
        sa.Column('current_page', sa.Integer(), nullable=True, default=0),
        sa.Column('total_pages', sa.Integer(), nullable=True, default=0),
        sa.Column('phase', sa.String(length=64), nullable=True),
        sa.Column('status_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('estimated_completion', sa.DateTime(), nullable=True),
        sa.Column('processing_time', sa.Float(), nullable=True),
        sa.Column('progress_data', sa.Text(), nullable=True),
        sa.Column('result_data', sa.Text(), nullable=True),
        sa.Column('error_data', sa.Text(), nullable=True),
        sa.Column('pages_analyzed', sa.Integer(), nullable=True, default=0),
        sa.Column('total_pages_found', sa.Integer(), nullable=True, default=0),
        sa.Column('failed_urls_count', sa.Integer(), nullable=True, default=0),
        sa.Column('ai_enhanced', sa.Boolean(), nullable=True, default=False),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes
    op.create_index('ix_seo_analyses_analysis_id', 'seo_analyses', ['analysis_id'], unique=True)
    op.create_index('ix_seo_analyses_user_id', 'seo_analyses', ['user_id'])
    op.create_index('ix_seo_analyses_url', 'seo_analyses', ['url'])
    op.create_index('ix_seo_analyses_mode', 'seo_analyses', ['mode'])
    op.create_index('ix_seo_analyses_status', 'seo_analyses', ['status'])
    op.create_index('ix_seo_analyses_created_at', 'seo_analyses', ['created_at'])


def downgrade():
    # Drop indexes
    op.drop_index('ix_seo_analyses_created_at', table_name='seo_analyses')
    op.drop_index('ix_seo_analyses_status', table_name='seo_analyses')
    op.drop_index('ix_seo_analyses_mode', table_name='seo_analyses')
    op.drop_index('ix_seo_analyses_url', table_name='seo_analyses')
    op.drop_index('ix_seo_analyses_user_id', table_name='seo_analyses')
    op.drop_index('ix_seo_analyses_analysis_id', table_name='seo_analyses')
    
    # Drop table
    op.drop_table('seo_analyses')
